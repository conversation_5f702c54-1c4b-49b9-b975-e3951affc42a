/**
 * Breeding Management API Routes
 *
 * This module provides API routes for breeding record management.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/breeding/records
 * @desc Get all breeding records
 * @access Private
 */
router.get('/records', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      femaleId,
      maleId,
      status,
      startDate,
      endDate,
      sort = 'breedingDate',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (femaleId) query.femaleId = femaleId;
    if (maleId) query.maleId = maleId;
    if (status) query.status = status;

    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get breeding records collection
    const breedingRecordsCollection = db.collection('breedingrecords');

    // Get breeding records with pagination
    const breedingRecords = await breedingRecordsCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await breedingRecordsCollection.countDocuments(query);

    // If femaleId or maleId is provided, get animal details
    if (femaleId || maleId) {
      const animalsCollection = db.collection('animals');

      // Get female animal details if femaleId is provided
      if (femaleId) {
        const femaleAnimal = await animalsCollection.findOne({ _id: femaleId });

        // Add female animal details to breeding records
        breedingRecords.forEach(record => {
          if (record.femaleId === femaleId) {
            record.femaleAnimal = femaleAnimal;
          }
        });
      }

      // Get male animal details if maleId is provided
      if (maleId) {
        const maleAnimal = await animalsCollection.findOne({ _id: maleId });

        // Add male animal details to breeding records
        breedingRecords.forEach(record => {
          if (record.maleId === maleId) {
            record.maleAnimal = maleAnimal;
          }
        });
      }
    }

    // Return breeding records
    res.json({
      success: true,
      data: breedingRecords,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting breeding records:', error);
    next(error);
  }
});

/**
 * @route GET /api/breeding/records/:id
 * @desc Get breeding record by ID
 * @access Private
 */
router.get('/records/:id', authenticate, async (req, res, next) => {
  try {
    // Get breeding record
    const breedingRecord = await ApiService.findById('BreedingRecord', req.params.id, {
      populate: ['femaleAnimal', 'maleAnimal']
    });

    // Check if breeding record exists
    if (!breedingRecord) {
      return res.status(404).json({
        success: false,
        message: 'Breeding record not found'
      });
    }

    // Return breeding record
    res.json({
      success: true,
      data: breedingRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/breeding/records
 * @desc Create a new breeding record
 * @access Private
 */
router.post('/records', authenticate, requirePermission(['create_breeding_record']), async (req, res, next) => {
  try {
    // Validate female animal exists
    if (req.body.femaleAnimal) {
      const femaleAnimal = await ApiService.findById('Animal', req.body.femaleAnimal);
      if (!femaleAnimal) {
        return res.status(404).json({
          success: false,
          message: 'Female animal not found'
        });
      }

      // Check if female animal is female
      if (femaleAnimal.gender !== 'female') {
        return res.status(400).json({
          success: false,
          message: 'Female animal must be female'
        });
      }
    }

    // Validate male animal exists
    if (req.body.maleAnimal) {
      const maleAnimal = await ApiService.findById('Animal', req.body.maleAnimal);
      if (!maleAnimal) {
        return res.status(404).json({
          success: false,
          message: 'Male animal not found'
        });
      }

      // Check if male animal is male
      if (maleAnimal.gender !== 'male') {
        return res.status(400).json({
          success: false,
          message: 'Male animal must be male'
        });
      }
    }

    // Create breeding record
    const breedingRecord = await ApiService.create('BreedingRecord', {
      ...req.body,
      createdBy: req.user.id
    });

    // Update female animal status if needed
    if (req.body.updateFemaleStatus && req.body.femaleAnimal) {
      await ApiService.updateById('Animal', req.body.femaleAnimal, {
        status: 'breeding'
      });
    }

    // Return breeding record
    res.status(201).json({
      success: true,
      data: breedingRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/breeding/records/:id
 * @desc Update a breeding record
 * @access Private
 */
router.put('/records/:id', authenticate, requirePermission(['update_breeding_record']), async (req, res, next) => {
  try {
    // Get breeding record
    const existingRecord = await ApiService.findById('BreedingRecord', req.params.id);

    // Check if breeding record exists
    if (!existingRecord) {
      return res.status(404).json({
        success: false,
        message: 'Breeding record not found'
      });
    }

    // Update breeding record
    const breedingRecord = await ApiService.updateById('BreedingRecord', req.params.id, req.body);

    // Update female animal status if needed
    if (req.body.status === 'confirmed' && existingRecord.femaleAnimal) {
      await ApiService.updateById('Animal', existingRecord.femaleAnimal, {
        status: 'pregnant',
        healthStatus: 'pregnant'
      });
    }

    // Return breeding record
    res.json({
      success: true,
      data: breedingRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/breeding/records/:id
 * @desc Delete a breeding record
 * @access Private
 */
router.delete('/records/:id', authenticate, requirePermission(['delete_breeding_record']), async (req, res, next) => {
  try {
    // Get breeding record
    const breedingRecord = await ApiService.findById('BreedingRecord', req.params.id);

    // Check if breeding record exists
    if (!breedingRecord) {
      return res.status(404).json({
        success: false,
        message: 'Breeding record not found'
      });
    }

    // Delete breeding record
    await ApiService.deleteById('BreedingRecord', req.params.id);

    // Return success
    res.json({
      success: true,
      data: breedingRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/breeding/births
 * @desc Get all birth records
 * @access Private
 */
router.get('/births', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      motherId,
      fatherId,
      startDate,
      endDate,
      sort = 'birthDate',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (motherId) query.mother = motherId;
    if (fatherId) query.father = fatherId;

    // Add date range if provided
    if (startDate || endDate) {
      query.birthDate = {};
      if (startDate) query.birthDate.$gte = new Date(startDate);
      if (endDate) query.birthDate.$lte = new Date(endDate);
    }

    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit),
      populate: ['mother', 'father', 'breedingRecord']
    };

    // Get birth records
    const birthRecords = await ApiService.find('BirthRecord', query, options);

    // Get total count
    const total = await ApiService.count('BirthRecord', query);

    // Return birth records
    res.json({
      success: true,
      data: birthRecords,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/breeding/births
 * @desc Create a new birth record
 * @access Private
 */
router.post('/births', authenticate, requirePermission(['create_birth_record']), async (req, res, next) => {
  try {
    // Validate mother animal exists
    if (req.body.mother) {
      const motherAnimal = await ApiService.findById('Animal', req.body.mother);
      if (!motherAnimal) {
        return res.status(404).json({
          success: false,
          message: 'Mother animal not found'
        });
      }

      // Check if mother animal is female
      if (motherAnimal.gender !== 'female') {
        return res.status(400).json({
          success: false,
          message: 'Mother animal must be female'
        });
      }
    }

    // Create birth record
    const birthRecord = await ApiService.create('BirthRecord', {
      ...req.body,
      createdBy: req.user.id
    });

    // Update mother animal status
    if (req.body.mother) {
      await ApiService.updateById('Animal', req.body.mother, {
        status: 'active',
        healthStatus: 'healthy'
      });
    }

    // Update breeding record if provided
    if (req.body.breedingRecord) {
      await ApiService.updateById('BreedingRecord', req.body.breedingRecord, {
        status: 'successful'
      });
    }

    // Return birth record
    res.status(201).json({
      success: true,
      data: birthRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/breeding/heat
 * @desc Get heat cycle records
 * @access Private
 */
router.get('/heat', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      animalId,
      status,
      startDate,
      endDate,
      sort = 'heatDate',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query for heat cycle records
    const query = {
      recordType: 'heat_cycle'
    };
    if (animalId) query.animalId = animalId;
    if (status) query.status = status;

    // Add date range if provided
    if (startDate || endDate) {
      query.heatDate = {};
      if (startDate) query.heatDate.$gte = new Date(startDate);
      if (endDate) query.heatDate.$lte = new Date(endDate);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get breeding records collection (heat cycles are stored as breeding records)
    const breedingRecordsCollection = db.collection('breeding_records');

    // Get heat cycle records with pagination
    const heatRecords = await breedingRecordsCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await breedingRecordsCollection.countDocuments(query);

    // Return heat cycle records
    res.json({
      success: true,
      data: heatRecords,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting heat cycle records:', error);
    next(error);
  }
});

/**
 * @route GET /api/breeding/stats
 * @desc Get breeding statistics
 * @access Private
 */
router.get('/stats', authenticate, async (req, res, next) => {
  try {
    // Get total breeding records
    const totalBreedingRecords = await ApiService.count('BreedingRecord');

    // Get total birth records
    const totalBirthRecords = await ApiService.count('BirthRecord');

    // Get breeding records by status
    const breedingByStatus = await ApiService.aggregate('BreedingRecord', [
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get recent breeding records (last 30 days)
    const recentBreedingRecords = await ApiService.count('BreedingRecord', {
      breedingDate: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get recent birth records (last 30 days)
    const recentBirthRecords = await ApiService.count('BirthRecord', {
      birthDate: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get total offspring
    const totalOffspring = await ApiService.aggregate('BirthRecord', [
      { $group: { _id: null, total: { $sum: '$totalOffspring' } } }
    ]);

    // Return statistics
    res.json({
      success: true,
      data: {
        totalBreedingRecords,
        totalBirthRecords,
        recentBreedingRecords,
        recentBirthRecords,
        totalOffspring: totalOffspring.length > 0 ? totalOffspring[0].total : 0,
        breedingByStatus: breedingByStatus.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

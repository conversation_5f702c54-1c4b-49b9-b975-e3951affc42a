/**
 * Financial Management API Routes
 *
 * This module provides API routes for financial management.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/financial/transactions
 * @desc Get all financial transactions
 * @access Private
 */
router.get('/transactions', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      type,
      category,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      sort = 'date',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (type) query.recordType = type;
    if (category) query.category = category;

    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Add amount range if provided
    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) query.amount.$gte = parseFloat(minAmount);
      if (maxAmount) query.amount.$lte = parseFloat(maxAmount);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get financial records collection
    const financialRecordsCollection = db.collection('financialrecords');

    // Get transactions with pagination
    const transactions = await financialRecordsCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await financialRecordsCollection.countDocuments(query);

    // Return transactions
    res.json({
      success: true,
      data: transactions,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting financial transactions:', error);
    next(error);
  }
});

/**
 * @route GET /api/financial/transactions/:id
 * @desc Get transaction by ID
 * @access Private
 */
router.get('/transactions/:id', authenticate, async (req, res, next) => {
  try {
    // Get transaction
    const transaction = await ApiService.findById('Transaction', req.params.id);

    // Check if transaction exists
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Return transaction
    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/financial/transactions
 * @desc Create a new transaction
 * @access Private
 */
router.post('/transactions', authenticate, requirePermission(['create_transaction']), async (req, res, next) => {
  try {
    // Create transaction
    const transaction = await ApiService.create('Transaction', {
      ...req.body,
      createdBy: req.user.id
    });

    // Return transaction
    res.status(201).json({
      success: true,
      data: transaction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/financial/transactions/:id
 * @desc Update a transaction
 * @access Private
 */
router.put('/transactions/:id', authenticate, requirePermission(['update_transaction']), async (req, res, next) => {
  try {
    // Update transaction
    const transaction = await ApiService.updateById('Transaction', req.params.id, req.body);

    // Check if transaction exists
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Return transaction
    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/financial/transactions/:id
 * @desc Delete a transaction
 * @access Private
 */
router.delete('/transactions/:id', authenticate, requirePermission(['delete_transaction']), async (req, res, next) => {
  try {
    // Get transaction
    const transaction = await ApiService.findById('Transaction', req.params.id);

    // Check if transaction exists
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Delete transaction
    await ApiService.deleteById('Transaction', req.params.id);

    // Return success
    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/financial/summary
 * @desc Get financial summary
 * @access Private
 */
router.get('/summary', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      startDate = new Date(new Date().getFullYear(), 0, 1).toISOString(), // Default to start of current year
      endDate = new Date().toISOString(), // Default to current date
      period = 'monthly' // Default to monthly
    } = req.query;

    // Convert dates to Date objects
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Get total income
    const totalIncome = await ApiService.aggregate('Transaction', [
      {
        $match: {
          type: 'income',
          transactionDate: { $gte: startDateObj, $lte: endDateObj }
        }
      },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Get total expenses
    const totalExpenses = await ApiService.aggregate('Transaction', [
      {
        $match: {
          type: 'expense',
          transactionDate: { $gte: startDateObj, $lte: endDateObj }
        }
      },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Get income by category
    const incomeByCategory = await ApiService.aggregate('Transaction', [
      {
        $match: {
          type: 'income',
          transactionDate: { $gte: startDateObj, $lte: endDateObj }
        }
      },
      { $group: { _id: '$category', total: { $sum: '$amount' } } },
      { $sort: { total: -1 } }
    ]);

    // Get expenses by category
    const expensesByCategory = await ApiService.aggregate('Transaction', [
      {
        $match: {
          type: 'expense',
          transactionDate: { $gte: startDateObj, $lte: endDateObj }
        }
      },
      { $group: { _id: '$category', total: { $sum: '$amount' } } },
      { $sort: { total: -1 } }
    ]);

    // Get income by period
    const incomeByPeriod = await getTransactionsByPeriod('income', startDateObj, endDateObj, period);

    // Get expenses by period
    const expensesByPeriod = await getTransactionsByPeriod('expense', startDateObj, endDateObj, period);

    // Calculate net profit
    const netProfit = (totalIncome.length > 0 ? totalIncome[0].total : 0) -
                     (totalExpenses.length > 0 ? totalExpenses[0].total : 0);

    // Return summary
    res.json({
      success: true,
      data: {
        totalIncome: totalIncome.length > 0 ? totalIncome[0].total : 0,
        totalExpenses: totalExpenses.length > 0 ? totalExpenses[0].total : 0,
        netProfit,
        incomeByCategory: incomeByCategory.reduce((acc, item) => {
          acc[item._id] = item.total;
          return acc;
        }, {}),
        expensesByCategory: expensesByCategory.reduce((acc, item) => {
          acc[item._id] = item.total;
          return acc;
        }, {}),
        incomeByPeriod,
        expensesByPeriod
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/financial/sales
 * @desc Get all sales records
 * @access Private
 */
router.get('/sales', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      animalId,
      buyerId,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      sort = 'saleDate',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query for sales (transactions with type 'sale' or category 'animal_sale')
    const query = {
      $or: [
        { recordType: 'sale' },
        { category: 'animal_sale' },
        { type: 'income', category: 'sales' }
      ]
    };

    if (animalId) query.animalId = animalId;
    if (buyerId) query.buyerId = buyerId;

    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Add amount range if provided
    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) query.amount.$gte = parseFloat(minAmount);
      if (maxAmount) query.amount.$lte = parseFloat(maxAmount);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get financial records collection
    const financialRecordsCollection = db.collection('financial_records');

    // Get sales with pagination
    const sales = await financialRecordsCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await financialRecordsCollection.countDocuments(query);

    // Return sales
    res.json({
      success: true,
      data: sales,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting sales records:', error);
    next(error);
  }
});

/**
 * @route GET /api/financial/budgets
 * @desc Get all budget records
 * @access Private
 */
router.get('/budgets', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      category,
      status,
      startDate,
      endDate,
      sort = 'createdAt',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query for budgets
    const query = {
      recordType: 'budget'
    };

    if (category) query.category = category;
    if (status) query.status = status;

    // Add date range if provided
    if (startDate || endDate) {
      query.period = {};
      if (startDate) query.period.$gte = new Date(startDate);
      if (endDate) query.period.$lte = new Date(endDate);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get financial records collection
    const financialRecordsCollection = db.collection('financial_records');

    // Get budgets with pagination
    const budgets = await financialRecordsCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await financialRecordsCollection.countDocuments(query);

    // Return budgets
    res.json({
      success: true,
      data: budgets,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting budget records:', error);
    next(error);
  }
});

/**
 * Get transactions by period
 * @param {string} type - Transaction type (income or expense)
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} period - Period (daily, weekly, monthly, quarterly, yearly)
 * @returns {Promise<Array>} Transactions by period
 */
async function getTransactionsByPeriod(type, startDate, endDate, period) {
  let groupBy = {};

  // Set group by based on period
  switch (period) {
    case 'daily':
      groupBy = {
        year: { $year: '$transactionDate' },
        month: { $month: '$transactionDate' },
        day: { $dayOfMonth: '$transactionDate' }
      };
      break;
    case 'weekly':
      groupBy = {
        year: { $year: '$transactionDate' },
        week: { $week: '$transactionDate' }
      };
      break;
    case 'monthly':
      groupBy = {
        year: { $year: '$transactionDate' },
        month: { $month: '$transactionDate' }
      };
      break;
    case 'quarterly':
      groupBy = {
        year: { $year: '$transactionDate' },
        quarter: { $ceil: { $divide: [{ $month: '$transactionDate' }, 3] } }
      };
      break;
    case 'yearly':
      groupBy = {
        year: { $year: '$transactionDate' }
      };
      break;
    default:
      groupBy = {
        year: { $year: '$transactionDate' },
        month: { $month: '$transactionDate' }
      };
  }

  // Get transactions by period
  const transactionsByPeriod = await ApiService.aggregate('Transaction', [
    {
      $match: {
        type,
        transactionDate: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: groupBy,
        total: { $sum: '$amount' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.week': 1, '_id.quarter': 1 } }
  ]);

  // Format result
  return transactionsByPeriod.map(item => {
    let period = '';

    // Format period based on groupBy
    if (item._id.day) {
      period = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`;
    } else if (item._id.week) {
      period = `${item._id.year}-W${item._id.week.toString().padStart(2, '0')}`;
    } else if (item._id.month) {
      period = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
    } else if (item._id.quarter) {
      period = `${item._id.year}-Q${item._id.quarter}`;
    } else {
      period = `${item._id.year}`;
    }

    return {
      period,
      total: item.total
    };
  });
}

module.exports = router;

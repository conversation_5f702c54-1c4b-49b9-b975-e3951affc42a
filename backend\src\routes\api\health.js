/**
 * Health Management API Routes
 *
 * This module provides API routes for health record management.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/health/records
 * @desc Get all health records
 * @access Private
 */
router.get('/records', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      animalId,
      recordType,
      status,
      startDate,
      endDate,
      sort = 'date',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (animalId) query.animalId = animalId;
    if (recordType) query.recordType = recordType;
    if (status) query.status = status;

    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get health records collection
    const healthRecordsCollection = db.collection('healthrecords');

    // Get health records with pagination
    const healthRecords = await healthRecordsCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await healthRecordsCollection.countDocuments(query);

    // If animalId is provided, get animal details
    if (animalId) {
      const animalsCollection = db.collection('animals');
      const animal = await animalsCollection.findOne({ _id: animalId });

      // Add animal details to health records
      healthRecords.forEach(record => {
        record.animal = animal;
      });
    }

    // Return health records
    res.json({
      success: true,
      data: healthRecords,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting health records:', error);
    next(error);
  }
});

/**
 * @route GET /api/health/records/:id
 * @desc Get health record by ID
 * @access Private
 */
router.get('/records/:id', authenticate, async (req, res, next) => {
  try {
    // Get health record
    const healthRecord = await ApiService.findById('HealthRecord', req.params.id, {
      populate: 'animal'
    });

    // Check if health record exists
    if (!healthRecord) {
      return res.status(404).json({
        success: false,
        message: 'Health record not found'
      });
    }

    // Return health record
    res.json({
      success: true,
      data: healthRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/health/records
 * @desc Create a new health record
 * @access Private
 */
router.post('/records', authenticate, requirePermission(['create_health_record']), async (req, res, next) => {
  try {
    // Validate animal exists
    if (req.body.animalId) {
      const animal = await ApiService.findById('Animal', req.body.animalId);
      if (!animal) {
        return res.status(404).json({
          success: false,
          message: 'Animal not found'
        });
      }
    }

    // Create health record
    const healthRecord = await ApiService.create('HealthRecord', {
      ...req.body,
      createdBy: req.user.id
    });

    // Update animal health status if provided
    if (req.body.updateAnimalHealth && req.body.animalId && req.body.healthStatus) {
      await ApiService.updateById('Animal', req.body.animalId, {
        healthStatus: req.body.healthStatus
      });
    }

    // Return health record
    res.status(201).json({
      success: true,
      data: healthRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/health/records/:id
 * @desc Update a health record
 * @access Private
 */
router.put('/records/:id', authenticate, requirePermission(['update_health_record']), async (req, res, next) => {
  try {
    // Update health record
    const healthRecord = await ApiService.updateById('HealthRecord', req.params.id, req.body);

    // Check if health record exists
    if (!healthRecord) {
      return res.status(404).json({
        success: false,
        message: 'Health record not found'
      });
    }

    // Update animal health status if provided
    if (req.body.updateAnimalHealth && healthRecord.animalId && req.body.healthStatus) {
      await ApiService.updateById('Animal', healthRecord.animalId, {
        healthStatus: req.body.healthStatus
      });
    }

    // Return health record
    res.json({
      success: true,
      data: healthRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/health/records/:id
 * @desc Delete a health record
 * @access Private
 */
router.delete('/records/:id', authenticate, requirePermission(['delete_health_record']), async (req, res, next) => {
  try {
    // Get health record
    const healthRecord = await ApiService.findById('HealthRecord', req.params.id);

    // Check if health record exists
    if (!healthRecord) {
      return res.status(404).json({
        success: false,
        message: 'Health record not found'
      });
    }

    // Delete health record
    await ApiService.deleteById('HealthRecord', req.params.id);

    // Return success
    res.json({
      success: true,
      data: healthRecord
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/health/stats
 * @desc Get health statistics
 * @access Private
 */
router.get('/stats', authenticate, async (req, res, next) => {
  try {
    // Get total health records
    const totalRecords = await ApiService.count('HealthRecord');

    // Get records by type
    const recordsByType = await ApiService.aggregate('HealthRecord', [
      { $group: { _id: '$recordType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get records by status
    const recordsByStatus = await ApiService.aggregate('HealthRecord', [
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get recent records (last 30 days)
    const recentRecords = await ApiService.count('HealthRecord', {
      date: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get upcoming records
    const upcomingRecords = await ApiService.count('HealthRecord', {
      status: 'scheduled',
      date: { $gte: new Date() }
    });

    // Get animals by health status
    const animalsByHealth = await ApiService.aggregate('Animal', [
      { $group: { _id: '$healthStatus', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Return statistics
    res.json({
      success: true,
      data: {
        totalRecords,
        recentRecords,
        upcomingRecords,
        byType: recordsByType.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byStatus: recordsByStatus.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        animalsByHealth: animalsByHealth.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/health/vaccinations
 * @desc Get vaccination schedule
 * @access Private
 */
router.get('/vaccinations', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      animalId,
      status,
      startDate,
      endDate,
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {
      recordType: 'vaccination'
    };

    if (animalId) query.animalId = animalId;
    if (status) query.status = status;

    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Build options
    const options = {
      sort: { date: 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit),
      populate: 'animal'
    };

    // Get vaccinations
    const vaccinations = await ApiService.find('HealthRecord', query, options);

    // Get total count
    const total = await ApiService.count('HealthRecord', query);

    // Return vaccinations
    res.json({
      success: true,
      data: vaccinations,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

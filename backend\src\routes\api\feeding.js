/**
 * Feeding API Routes
 *
 * This module provides API routes for feeding management.
 */

const express = require('express');
const router = express.Router();
const { authenticate, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/feeding/records
 * @desc Get all feeding records
 * @access Private
 */
router.get('/records', async (req, res, next) => {
  try {
    // Return mock feeding records for now
    return res.json({
      success: true,
      data: [
        { id: '1', animalId: '1', feedType: 'Hay', quantity: 5, unit: 'kg', date: new Date() },
        { id: '2', animalId: '2', feedType: 'Grain', quantity: 2, unit: 'kg', date: new Date() },
        { id: '3', animalId: '3', feedType: 'Silage', quantity: 10, unit: 'kg', date: new Date() }
      ]
    });
  } catch (error) {
    logger.error('Error getting feeding records:', error);
    next(error);
  }
});

/**
 * @route GET /api/feeding/inventory
 * @desc Get all feed inventory
 * @access Private
 */
router.get('/inventory', async (req, res, next) => {
  try {
    // Return mock feed inventory for now
    return res.json({
      success: true,
      data: [
        { id: '1', feedType: 'Hay', quantity: 500, unit: 'kg', location: 'Barn 1' },
        { id: '2', feedType: 'Grain', quantity: 200, unit: 'kg', location: 'Silo 1' },
        { id: '3', feedType: 'Silage', quantity: 1000, unit: 'kg', location: 'Silo 2' }
      ]
    });
  } catch (error) {
    logger.error('Error getting feed inventory:', error);
    next(error);
  }
});

/**
 * @route GET /api/feeding/plans
 * @desc Get all feeding plans
 * @access Private
 */
router.get('/plans', async (req, res, next) => {
  try {
    // Return mock feeding plans for now
    return res.json({
      success: true,
      data: [
        { id: '1', name: 'Cattle Winter Plan', description: 'Winter feeding plan for cattle', animalType: 'cattle' },
        { id: '2', name: 'Sheep Summer Plan', description: 'Summer feeding plan for sheep', animalType: 'sheep' },
        { id: '3', name: 'Goat Standard Plan', description: 'Standard feeding plan for goats', animalType: 'goat' }
      ]
    });
  } catch (error) {
    logger.error('Error getting feeding plans:', error);
    next(error);
  }
});

module.exports = router;

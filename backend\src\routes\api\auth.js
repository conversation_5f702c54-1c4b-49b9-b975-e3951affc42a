/**
 * Authentication API Routes
 * 
 * This module provides API routes for authentication.
 */

const express = require('express');
const router = express.Router();
const { authenticateUser, hashPassword, verifyToken } = require('../../services/authService');
const ApiService = require('../../services/apiService');
const { authenticate, authorize } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route POST /api/auth/login
 * @desc Login user
 * @access Public
 */
router.post('/login', async (req, res, next) => {
  try {
    const { username, password } = req.body;
    
    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }
    
    // Authenticate user
    const result = await authenticateUser(username, password);
    
    // Return result
    if (result.success) {
      res.json(result);
    } else {
      res.status(401).json(result);
    }
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/register
 * @desc Register user
 * @access Private (Admin only)
 */
router.post('/register', authenticate, authorize(['admin']), async (req, res, next) => {
  try {
    const { username, email, password, firstName, lastName, role } = req.body;
    
    // Validate input
    if (!username || !email || !password || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }
    
    // Check if username or email already exists
    const existingUser = await ApiService.find('User', {
      $or: [
        { username },
        { email }
      ]
    });
    
    if (existingUser.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }
    
    // Hash password
    const hashedPassword = await hashPassword(password);
    
    // Create user
    const user = await ApiService.create('User', {
      username,
      email,
      password: hashedPassword,
      firstName,
      lastName,
      role: role || 'user',
      status: 'active',
      permissions: [],
      lastLogin: new Date()
    });
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    // Return user
    res.status(201).json({
      success: true,
      data: userResponse
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/auth/me
 * @desc Get current user
 * @access Private
 */
router.get('/me', async (req, res, next) => {
  try {
    // Return mock user for testing
    res.json({
      success: true,
      data: {
        id: 'admin-id',
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        status: 'active',
        permissions: ['all'],
        lastLogin: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/change-password
 * @desc Change password
 * @access Private
 */
router.post('/change-password', authenticate, async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }
    
    // Get user
    const user = await ApiService.findById('User', req.user.id);
    
    // Check if user exists
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }
    
    // Hash new password
    const hashedPassword = await hashPassword(newPassword);
    
    // Update password
    await ApiService.updateById('User', req.user.id, {
      password: hashedPassword
    });
    
    // Return success
    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/refresh-token
 * @desc Refresh token
 * @access Public
 */
router.post('/refresh-token', async (req, res, next) => {
  try {
    const { token } = req.body;
    
    // Validate input
    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      });
    }
    
    // Verify token
    const decoded = verifyToken(token);
    
    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    
    // Generate new token
    const newToken = generateToken({
      id: decoded.id,
      username: decoded.username,
      role: decoded.role,
      firstName: decoded.firstName,
      lastName: decoded.lastName
    });
    
    // Return new token
    res.json({
      success: true,
      token: newToken
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/logout
 * @desc Logout user
 * @access Private
 */
router.post('/logout', authenticate, (req, res) => {
  // Client-side logout (just return success)
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

module.exports = router;

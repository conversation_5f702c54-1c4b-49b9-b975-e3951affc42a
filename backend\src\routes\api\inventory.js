/**
 * Inventory Management API Routes
 *
 * This module provides API routes for inventory management.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/inventory
 * @desc Get inventory overview
 * @access Private
 */
router.get('/', async (req, res, next) => {
  try {
    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get inventory collection
    const inventoryCollection = db.collection('inventory');

    // Get inventory items (limited to 10)
    const items = await inventoryCollection.find({})
      .sort({ name: 1 })
      .limit(10)
      .toArray();

    // Get total count
    const total = await inventoryCollection.countDocuments({});

    // Return inventory overview
    res.json({
      success: true,
      data: {
        recentItems: items,
        totalItems: total,
        categories: {
          feed: await inventoryCollection.countDocuments({ category: 'feed' }),
          medication: await inventoryCollection.countDocuments({ category: 'medication' }),
          equipment: await inventoryCollection.countDocuments({ category: 'equipment' }),
          supplies: await inventoryCollection.countDocuments({ category: 'supplies' })
        }
      }
    });
  } catch (error) {
    logger.error('Error getting inventory overview:', error);
    next(error);
  }
});

/**
 * @route GET /api/inventory/items
 * @desc Get all inventory items
 * @access Private
 */
router.get('/items', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      category,
      status,
      search,
      minQuantity,
      maxQuantity,
      sort = 'name',
      order = 'asc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (category) query.category = category;
    if (status) query.status = status;

    // Add search if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }

    // Add quantity range if provided
    if (minQuantity || maxQuantity) {
      query.quantity = {};
      if (minQuantity) query.quantity.$gte = parseInt(minQuantity);
      if (maxQuantity) query.quantity.$lte = parseInt(maxQuantity);
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get inventory collection
    const inventoryCollection = db.collection('inventory');

    // Get inventory items with pagination
    const items = await inventoryCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await inventoryCollection.countDocuments(query);

    // Return inventory items
    res.json({
      success: true,
      data: items,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting inventory items:', error);
    next(error);
  }
});

/**
 * @route GET /api/inventory/items/:id
 * @desc Get inventory item by ID
 * @access Private
 */
router.get('/items/:id', authenticate, async (req, res, next) => {
  try {
    // Get inventory item
    const item = await ApiService.findById('InventoryItem', req.params.id);

    // Check if inventory item exists
    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    // Return inventory item
    res.json({
      success: true,
      data: item
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/inventory/items
 * @desc Create a new inventory item
 * @access Private
 */
router.post('/items', authenticate, requirePermission(['create_inventory']), async (req, res, next) => {
  try {
    // Create inventory item
    const item = await ApiService.create('InventoryItem', {
      ...req.body,
      createdBy: req.user.id
    });

    // Return inventory item
    res.status(201).json({
      success: true,
      data: item
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/inventory/items/:id
 * @desc Update an inventory item
 * @access Private
 */
router.put('/items/:id', authenticate, requirePermission(['update_inventory']), async (req, res, next) => {
  try {
    // Update inventory item
    const item = await ApiService.updateById('InventoryItem', req.params.id, req.body);

    // Check if inventory item exists
    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    // Return inventory item
    res.json({
      success: true,
      data: item
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/inventory/items/:id
 * @desc Delete an inventory item
 * @access Private
 */
router.delete('/items/:id', authenticate, requirePermission(['delete_inventory']), async (req, res, next) => {
  try {
    // Get inventory item
    const item = await ApiService.findById('InventoryItem', req.params.id);

    // Check if inventory item exists
    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    // Delete inventory item
    await ApiService.deleteById('InventoryItem', req.params.id);

    // Return success
    res.json({
      success: true,
      data: item
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/inventory/transactions
 * @desc Create a new inventory transaction
 * @access Private
 */
router.post('/transactions', authenticate, requirePermission(['create_inventory']), async (req, res, next) => {
  try {
    const { itemId, transactionType, quantity, date, notes } = req.body;

    // Validate required fields
    if (!itemId || !transactionType || !quantity || !date) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Get inventory item
    const item = await ApiService.findById('InventoryItem', itemId);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    // Create transaction
    const transaction = await ApiService.create('InventoryTransaction', {
      itemId,
      itemName: item.name,
      transactionType,
      quantity: parseInt(quantity),
      date,
      performedBy: req.user.id,
      notes,
      createdBy: req.user.id
    });

    // Update inventory item quantity
    let newQuantity = item.quantity;

    if (transactionType === 'purchase') {
      newQuantity += parseInt(quantity);
    } else if (transactionType === 'use') {
      newQuantity -= parseInt(quantity);

      // Prevent negative quantity
      if (newQuantity < 0) {
        newQuantity = 0;
      }
    }

    await ApiService.updateById('InventoryItem', itemId, { quantity: newQuantity });

    // Return transaction
    res.status(201).json({
      success: true,
      data: transaction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/inventory/transactions
 * @desc Get all inventory transactions
 * @access Private
 */
router.get('/transactions', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      itemId,
      transactionType,
      startDate,
      endDate,
      sort = 'date',
      order = 'desc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (itemId) query.itemId = itemId;
    if (transactionType) query.transactionType = transactionType;

    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get transactions
    const transactions = await ApiService.find('InventoryTransaction', query, options);

    // Get total count
    const total = await ApiService.count('InventoryTransaction', query);

    // Return transactions
    res.json({
      success: true,
      data: transactions,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/inventory/suppliers
 * @desc Get all suppliers
 * @access Private
 */
router.get('/suppliers', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      category,
      search,
      sort = 'name',
      order = 'asc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (category) query.category = category;

    // Add search if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { contactPerson: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get suppliers
    const suppliers = await ApiService.find('Supplier', query, options);

    // Get total count
    const total = await ApiService.count('Supplier', query);

    // Return suppliers
    res.json({
      success: true,
      data: suppliers,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/inventory/suppliers
 * @desc Create a new supplier
 * @access Private
 */
router.post('/suppliers', authenticate, requirePermission(['create_inventory']), async (req, res, next) => {
  try {
    // Create supplier
    const supplier = await ApiService.create('Supplier', {
      ...req.body,
      createdBy: req.user.id
    });

    // Return supplier
    res.status(201).json({
      success: true,
      data: supplier
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/inventory/stats
 * @desc Get inventory statistics
 * @access Private
 */
router.get('/stats', authenticate, async (req, res, next) => {
  try {
    // Get total inventory items
    const totalItems = await ApiService.count('InventoryItem');

    // Get items by category
    const itemsByCategory = await ApiService.aggregate('InventoryItem', [
      { $group: { _id: '$category', count: { $sum: 1 }, value: { $sum: { $multiply: ['$quantity', '$unitPrice'] } } } },
      { $sort: { count: -1 } }
    ]);

    // Get low stock items (less than 10 units)
    const lowStockItems = await ApiService.count('InventoryItem', { quantity: { $lt: 10 } });

    // Get out of stock items
    const outOfStockItems = await ApiService.count('InventoryItem', { quantity: 0 });

    // Get total inventory value
    const inventoryValue = await ApiService.aggregate('InventoryItem', [
      { $group: { _id: null, total: { $sum: { $multiply: ['$quantity', '$unitPrice'] } } } }
    ]);

    // Get recent transactions (last 30 days)
    const recentTransactions = await ApiService.count('InventoryTransaction', {
      date: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Return statistics
    res.json({
      success: true,
      data: {
        totalItems,
        lowStockItems,
        outOfStockItems,
        inventoryValue: inventoryValue.length > 0 ? inventoryValue[0].total : 0,
        recentTransactions,
        itemsByCategory: itemsByCategory.reduce((acc, item) => {
          acc[item._id] = {
            count: item.count,
            value: item.value
          };
          return acc;
        }, {})
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

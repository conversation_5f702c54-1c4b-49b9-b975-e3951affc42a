/**
 * Resources API Routes
 *
 * This module provides API routes for resources management.
 */

const express = require('express');
const router = express.Router();
const { authenticate, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/resources/stats
 * @desc Get resources statistics
 * @access Private
 */
router.get('/stats', async (req, res, next) => {
  try {
    // Return mock statistics for now
    return res.json({
      success: true,
      data: {
        totalResources: 45,
        categories: {
          guides: 15,
          training: 20,
          support: 10
        },
        recentlyAdded: 5,
        mostViewed: [
          { id: '1', title: 'Cattle Breeding Guide', views: 120 },
          { id: '2', title: 'Animal Health Basics', views: 98 },
          { id: '3', title: 'Feed Management', views: 87 }
        ]
      }
    });
  } catch (error) {
    logger.error('Error getting resources statistics:', error);
    next(error);
  }
});

/**
 * @route GET /api/resources/guides
 * @desc Get all guides
 * @access Private
 */
router.get('/guides', async (req, res, next) => {
  try {
    // Return mock guides for now
    return res.json({
      success: true,
      data: [
        { id: '1', title: 'Cattle Breeding Guide', category: 'breeding', downloads: 120 },
        { id: '2', title: 'Animal Health Basics', category: 'health', downloads: 98 },
        { id: '3', title: 'Feed Management', category: 'feeding', downloads: 87 }
      ]
    });
  } catch (error) {
    logger.error('Error getting guides:', error);
    next(error);
  }
});

/**
 * @route GET /api/resources/training
 * @desc Get all training resources
 * @access Private
 */
router.get('/training', async (req, res, next) => {
  try {
    // Return mock training resources for now
    return res.json({
      success: true,
      data: [
        { id: '1', title: 'Animal Handling Training', category: 'handling', duration: '2 hours' },
        { id: '2', title: 'Vaccination Techniques', category: 'health', duration: '1.5 hours' },
        { id: '3', title: 'Feed Mixing Workshop', category: 'feeding', duration: '3 hours' }
      ]
    });
  } catch (error) {
    logger.error('Error getting training resources:', error);
    next(error);
  }
});

/**
 * @route GET /api/resources/support
 * @desc Get all support resources
 * @access Private
 */
router.get('/support', async (req, res, next) => {
  try {
    // Return mock support resources for now
    return res.json({
      success: true,
      data: [
        { id: '1', title: 'Technical Support', category: 'technical', contact: '<EMAIL>' },
        { id: '2', title: 'Veterinary Assistance', category: 'veterinary', contact: '<EMAIL>' },
        { id: '3', title: 'Financial Advice', category: 'financial', contact: '<EMAIL>' }
      ]
    });
  } catch (error) {
    logger.error('Error getting support resources:', error);
    next(error);
  }
});

module.exports = router;

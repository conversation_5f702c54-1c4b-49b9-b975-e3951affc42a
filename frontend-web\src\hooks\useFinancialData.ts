import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Transaction,
  Budget,
  SalesRecord
} from '../types/financial';

const API_URL = process.env.REACT_APP_API_URL;

export interface FinancialStats {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  growthRate: number;
  budgetUtilization: number;
  pendingPayments: number;
  recentTransactions: number;
}

export interface BudgetSummary {
  category: string;
  allocated: number;
  spent: number;
  percentage: number;
}

export const useFinancialData = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [salesRecords, setSalesRecords] = useState<SalesRecord[]>([]);
  const [budgets, setBudgets] = useState<BudgetSummary[]>([]);
  const [stats, setStats] = useState<FinancialStats>({
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    growthRate: 0,
    budgetUtilization: 0,
    pendingPayments: 0,
    recentTransactions: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFinancialData = async () => {
    try {
      setLoading(true);
      const [transactionsRes, salesRes, budgetsRes] = await Promise.all([
        axios.get(`${API_URL}/financial/transactions`),
        axios.get(`${API_URL}/financial/sales`),
        axios.get(`${API_URL}/financial/budgets`)
      ]);

      setTransactions(transactionsRes.data);
      setSalesRecords(salesRes.data);
      setBudgets(budgetsRes.data);
      calculateFinancialStats();
    } catch (err) {
      setError('Failed to fetch financial data');
    } finally {
      setLoading(false);
    }
  };

  const calculateFinancialStats = () => {
    const totalRevenue = salesRecords.reduce((sum, sale) => sum + sale.totalAmount, 0);
    const totalExpenses = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    setStats({
      totalRevenue,
      totalExpenses,
      netProfit: totalRevenue - totalExpenses,
      growthRate: calculateGrowthRate(),
      budgetUtilization: calculateBudgetUtilization(),
      pendingPayments: calculatePendingPayments(),
      recentTransactions: transactions.filter(t =>
        new Date(t.date) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length
    });
  };

  const calculateGrowthRate = () => {
    const currentMonth = new Date().getMonth();
    const currentYearSales = salesRecords
      .filter(sale => new Date(sale.date).getFullYear() === new Date().getFullYear())
      .reduce((sum, sale) => sum + sale.totalAmount, 0);

    const lastYearSales = salesRecords
      .filter(sale => new Date(sale.date).getFullYear() === new Date().getFullYear() - 1)
      .reduce((sum, sale) => sum + sale.totalAmount, 0);

    return lastYearSales ? ((currentYearSales - lastYearSales) / lastYearSales) * 100 : 0;
  };

  const calculateBudgetUtilization = () => {
    const totalBudget = budgets.reduce((sum, budget) => sum + budget.allocated, 0);
    const totalSpent = budgets.reduce((sum, budget) => sum + budget.spent, 0);
    return totalBudget ? (totalSpent / totalBudget) * 100 : 0;
  };

  const calculatePendingPayments = () => {
    return salesRecords
      .filter(sale => sale.paymentStatus === 'pending')
      .reduce((sum, sale) => sum + sale.totalAmount, 0);
  };

  useEffect(() => {
    fetchFinancialData();
  }, []);

  return {
    transactions,
    salesRecords,
    budgets,
    stats,
    loading,
    error,
    fetchFinancialData,
    calculateFinancialStats
  };
};

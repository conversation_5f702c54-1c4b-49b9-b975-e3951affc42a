import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Paper,
  Container,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress
} from '@mui/material';
import { Person, Lock, Visibility, VisibilityOff } from '@mui/icons-material';

// Removed duplicate variable declarations and fixed references
const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const { login, isLoading, error: authError } = useAuth();
  const { translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/dashboard';

  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !password) {
      setError(translate('login.error.empty_fields'));
      return;
    }
    try {
      await login(username, password);
      navigate(from, { replace: true });
    } catch (err) {
      setError(translate('login.error.invalid_credentials'));
    }
  };

  // Debug logging
  console.log('Login component rendering');
  console.log('Username:', username);
  console.log('Password length:', password.length);
  console.log('Error:', error);
  console.log('Auth error:', authError);
  console.log('Is loading:', isLoading);

  return (
    <React.Fragment>
      <div className="login-page" style={{ backgroundColor: '#0f766e', minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Container maxWidth="sm">
          <Paper elevation={3} className="login-paper" style={{ padding: '40px', backgroundColor: 'white', borderRadius: '12px' }}>
            <Typography variant="h4" align="center" gutterBottom style={{ color: '#333', marginBottom: '24px' }}>
              {translate('login.title', { fallback: 'Welcome Back' })}
            </Typography>
            {error && <Alert severity="error" style={{ marginBottom: '16px' }}>{error}</Alert>}
            <form onSubmit={handleLogin}>
              <TextField
                label={translate('login.username', { fallback: 'Username' })}
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                fullWidth
                margin="normal"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person />
                    </InputAdornment>
                  ),
                }}
              />
              <TextField
                label={translate('login.password', { fallback: 'Password' })}
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                fullWidth
                margin="normal"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowPassword(!showPassword)}>
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={isLoading}
                className="login-button"
                style={{ marginTop: '24px', height: '48px', fontSize: '16px', fontWeight: 600 }}
              >
                {isLoading ? <CircularProgress size={24} /> : translate('login.sign_in', { fallback: 'SIGN IN' })}
              </Button>
            </form>
          </Paper>
        </Container>
      </div>
    </React.Fragment>
  );
};

export default Login;

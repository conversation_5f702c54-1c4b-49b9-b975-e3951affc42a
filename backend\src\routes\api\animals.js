/**
 * Animals API Routes
 *
 * This module provides API routes for animal management.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/animals
 * @desc Get all animals
 * @access Private
 */
router.get('/', async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      species,
      status,
      gender,
      location,
      sort = 'tagNumber',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (species) query.species = species;
    if (status) query.status = status;
    if (gender) query.gender = gender;
    if (location) query.location = location;

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get animals collection
    const animalsCollection = db.collection('animals');

    // Get animals with pagination
    const animals = await animalsCollection.find(query)
      .sort({ [sort]: 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await animalsCollection.countDocuments(query);

    // Return animals
    res.json({
      success: true,
      data: animals,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting animals:', error);
    next(error);
  }
});

/**
 * @route GET /api/animals/stats
 * @desc Get animal statistics
 * @access Private
 */
router.get('/stats', async (req, res, next) => {
  try {
    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get animals collection
    const animalsCollection = db.collection('animals');

    // Get total animals
    const totalAnimals = await animalsCollection.countDocuments();

    // Get animals by species
    const animalsBySpecies = await animalsCollection.aggregate([
      { $group: { _id: '$species', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    // Get animals by status
    const animalsByStatus = await animalsCollection.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    // Get animals by location
    const animalsByLocation = await animalsCollection.aggregate([
      { $group: { _id: '$location', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    // Get animals by health status
    const animalsByHealth = await animalsCollection.aggregate([
      { $group: { _id: '$healthStatus', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    // Get recent additions (last 30 days)
    const recentAdditions = await animalsCollection.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get active animals
    const activeAnimals = await animalsCollection.countDocuments({ status: 'active' });

    // Get healthy animals
    const healthyAnimals = await animalsCollection.countDocuments({ healthStatus: 'healthy' });
    const healthPercentage = totalAnimals > 0 ? (healthyAnimals / totalAnimals) * 100 : 0;

    // Return statistics
    res.json({
      success: true,
      data: {
        totalAnimals,
        activeAnimals,
        recentAdditions,
        healthPercentage,
        bySpecies: animalsBySpecies.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byStatus: animalsByStatus.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byLocation: animalsByLocation.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byHealth: animalsByHealth.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      }
    });
  } catch (error) {
    logger.error('Error getting animal statistics:', error);
    next(error);
  }
});

/**
 * @route GET /api/animals/:id
 * @desc Get animal by ID
 * @access Private
 */
router.get('/:id', authenticate, async (req, res, next) => {
  try {
    // Get animal
    const animal = await ApiService.findById('Animal', req.params.id);

    // Check if animal exists
    if (!animal) {
      return res.status(404).json({
        success: false,
        message: 'Animal not found'
      });
    }

    // Return animal
    res.json({
      success: true,
      data: animal
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/animals
 * @desc Create a new animal
 * @access Private
 */
router.post('/', authenticate, requirePermission(['create_animal']), async (req, res, next) => {
  try {
    // Create animal
    const animal = await ApiService.create('Animal', {
      ...req.body,
      createdBy: req.user.id
    });

    // Return animal
    res.status(201).json({
      success: true,
      data: animal
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/animals/:id
 * @desc Update an animal
 * @access Private
 */
router.put('/:id', authenticate, requirePermission(['update_animal']), async (req, res, next) => {
  try {
    // Update animal
    const animal = await ApiService.updateById('Animal', req.params.id, req.body);

    // Check if animal exists
    if (!animal) {
      return res.status(404).json({
        success: false,
        message: 'Animal not found'
      });
    }

    // Return animal
    res.json({
      success: true,
      data: animal
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/animals/:id
 * @desc Delete an animal
 * @access Private
 */
router.delete('/:id', authenticate, requirePermission(['delete_animal']), async (req, res, next) => {
  try {
    // Delete animal
    const animal = await ApiService.deleteById('Animal', req.params.id);

    // Check if animal exists
    if (!animal) {
      return res.status(404).json({
        success: false,
        message: 'Animal not found'
      });
    }

    // Return success
    res.json({
      success: true,
      data: animal
    });
  } catch (error) {
    next(error);
  }
});



module.exports = router;

{"login.welcome": "Welcome Back", "login.subtitle": "Sign in to your MayCaiphus Livestock account", "login.username": "Username", "login.password": "Password", "login.signin": "SIGN IN", "login.sign_in": "SIGN IN", "login.title": "Welcome Back", "login.footer_text": "MayCaiphus Livestock Management System", "login.enter_username": "Enter your username", "login.enter_password": "Enter your password", "login.error.credentials": "Invalid username or password", "login.error.empty_fields": "Please enter both username and password", "login.error.invalid_credentials": "Invalid username or password", "login.language_selection": "Select Language", "login.forgot_password": "I forgot password?", "login.contact_admin_for_password_reset": "Please contact your system administrator to reset your password.", "dashboard.title": "Dashboard", "dashboard.welcome": "Welcome to MayCaiphus Livestock", "dashboard.subtitle": "Your comprehensive solution for efficient livestock management", "dashboard.preparing": "Preparing your dashboard...", "dashboard.modules": "Management Modules", "dashboard.sponsors": "Our Sponsors", "dashboard.system_overview": "System Overview", "dashboard.kpi_description": "Key performance indicators and system status", "dashboard.health_trends": "Livestock Health Trends", "dashboard.weekly_health": "Weekly health status overview", "dashboard.financial_performance": "Financial Performance", "dashboard.monthly_revenue": "Monthly revenue and expenses", "dashboard.animals_attention": "{{count}} need attention", "dashboard.pending_checkups": "{{count}} pending checkups", "dashboard.due_dates": "{{count}} due dates soon", "dashboard.low_stock": "{{count}} low stock alerts", "dashboard.revenue_month": "Revenue this month", "dashboard.new_insights": "{{count}} new insights", "dashboard.analytics": "Analytics", "dashboard.sponsor": "Sponsor {{number}}", "dashboard.sponsor_alt": "Sponsor {{number}}", "dashboard.stat.total_animals": "Total Animals", "dashboard.stat.active_alerts": "Active Alerts", "dashboard.stat.pending_tasks": "Pending Tasks", "dashboard.stat.system_health": "System Health", "dashboard.stat.total_revenue": "Total Revenue", "dashboard.stat.feed_stock": "Feed Stock", "common.loading": "Loading...", "common.error": "An error occurred", "common.success": "Success", "common.save": "Save", "common.cancel": "Cancel", "common.delete": "Delete", "common.edit": "Edit", "common.add": "Add", "common.search": "Search", "common.filter": "Filter", "common.sort": "Sort", "common.view": "View", "common.download": "Download", "common.export": "Export", "common.import": "Import", "common.print": "Print", "common.close": "Close", "common.confirm": "Confirm", "common.back": "Back", "common.next": "Next", "common.previous": "Previous", "common.submit": "Submit", "common.reset": "Reset", "common.refresh": "Refresh", "common.help": "Help", "common.active": "Active", "common.new": "New", "nav.dashboard": "Dashboard", "nav.animals": "Animals", "nav.breeding": "Breeding", "nav.health": "Health", "nav.feeding": "Feeding", "nav.financial": "Financial", "nav.reports": "Reports", "nav.settings": "Settings", "nav.logout": "Logout", "nav.profile": "Profile", "nav.commercial": "Commercial", "nav.resources": "Resources", "nav.compliance": "Compliance", "nav.analytics": "Business Analytics", "nav.asset_management": "Asset Management", "animals.dashboard": "Animals Dashboard", "animals.manage": "Manage your livestock and animal records", "animals.add": "Add Animal", "animals.total": "Total Animals", "animals.active": "Active Animals", "animals.healthy": "Healthy Animals", "animals.animals": "animals", "animals.inventory.title": "Livestock Inventory", "animals.inventory.subtitle": "Manage and monitor your animal inventory with comprehensive tools", "animals.search_placeholder": "Search animals...", "animals.no_animals_found": "No animals found matching your criteria", "animals.location": "Location", "animals.weight": "Weight", "animals.age": "Age", "animals.years": "years", "animals.not_specified": "Not specified", "animals.not_recorded": "Not recorded", "animals.unknown": "Unknown", "animals.filter.all": "All Animals", "animals.filter.healthy": "Healthy", "animals.filter.attention": "Needs Attention", "animals.filter.critical": "Critical", "animals.filter.pregnant": "Pregnant", "animals.species.cattle": "Cattle", "animals.species.sheep": "Sheep", "animals.species.goat": "Goa<PERSON>", "animals.species.horse": "Horse", "animals.species.game": "Game", "animals.dashboard.title": "Animal Management", "animals.dashboard.subtitle": "Manage your livestock inventory and track animal information", "animals.dashboard.action": "Add Animal", "animals.species_distribution": "Animals by Species", "animals.species_distribution_desc": "Distribution of animals by species type", "animals.species_distribution_help": "Click on a segment to see details about that species", "animals.health_distribution": "Health Status Distribution", "animals.health_distribution_desc": "Distribution of animals by health status", "animals.health_distribution_help": "Shows the proportion of healthy, sick, injured, and pregnant animals", "animals.location_distribution": "Animals by Location", "animals.location_distribution_desc": "Distribution of animals by location", "animals.location_distribution_help": "Shows where your animals are located", "animals.growth_chart": "Growth Trends", "animals.growth_chart_desc": "Monthly weight and height progression", "animals.growth_chart_help": "Track animal growth over time", "animals.animal_growth_trends": "Animal Growth Trends", "animals.species_details": "Species Details", "animals.growth_details": "Growth Details", "animals.chart_details": "Chart Details", "animals.asset_overview": "Asset Management Overview", "animals.asset_overview_desc": "Financial overview of your livestock assets", "animals.total_asset_value": "Total Asset Value", "animals.average_roi": "Average ROI", "animals.active_assets": "Active Assets", "animals.retired_assets": "Retired Assets", "animals.asset_status": "Asset Status", "animals.retired": "Retired", "animals.near_retirement": "Near Retirement", "animals.view_details": "View Details", "animals.retirement_reasons": "Retirement Reasons", "animals.retirement_reasons_desc": "Reasons for animal retirement", "animals.retirement_reasons_help": "Shows the distribution of retirement reasons", "animals.registry": "Animal Registry", "animals.registry_desc": "Browse your registered animals", "animals.featured": "Featured Animals", "animals.view_all": "View All Animals", "animals.profiles": "Animal Profiles", "animals.profiles.subtitle": "Manage and view detailed profiles of all livestock", "animals.profiles.action": "Add New Animal", "animals.list.title": "Animal List", "animals.list.subtitle": "Manage and view all your livestock", "animals.list.action": "Add Animal", "animals.tag": "Tag", "animals.breed": "Breed", "animals.born": "Born", "animals.status": "Status", "dashboard.growth_rate": "Growth Rate", "dashboard.since_last_month": "since last month", "animals.inactive": "Inactive Animals", "animals.sick": "Sick Animals", "animals.pregnant": "Pregnant Animals", "animals.tracked": "Animals Tracked", "animals.tracking": "Location Tracking", "animals.genealogy": "Genealogy", "animals.rfid": "QR/RFID Management", "animals.asset": "Asset Management", "asset_management.description": "Manage livestock assets and retirement planning", "asset_management.retirement": "Retirement Tracking", "asset_management.dashboard.title": "Asset Management", "asset_management.dashboard.subtitle": "Track and manage livestock assets, retirement planning, and performance metrics", "asset_management.total_animals": "Total Animals", "asset_management.active_animals": "Active Animals", "asset_management.retired_animals": "Retired Animals", "asset_management.nearing_retirement": "Nearing Retirement", "asset_management.value_active": "Value of Active Assets", "asset_management.value_retired": "Value of Retired Assets", "asset_management.total_value": "Total Asset Value", "asset_management.retirement_candidates": "Retirement Candidates", "asset_management.retirement_reason": "Retirement Reason", "asset_management.age": "Age", "asset_management.breeding_count": "Breeding Count", "breeding.dashboard": "Breeding Dashboard", "breeding.dashboard.title": "Breeding Management", "breeding.dashboard.subtitle": "Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions", "breeding.dashboard.action": "Add Breeding Record", "breeding.manage": "Manage breeding records and schedules", "breeding.add": "Add Breeding Record", "breeding.breeding_statistics": "Breeding Statistics", "breeding.breeding_analytics": "Breeding Analytics", "breeding.upcoming_events": "Upcoming Events", "breeding.total_breedings": "Total Breedings", "breeding.success_rate": "Success Rate", "breeding.upcoming_births": "Upcoming Births", "breeding.heat_cycles": "Heat Cycles", "breeding.heat": "Heat Detection", "breeding.schedule": "Breeding Schedule", "breeding.schedule.subtitle": "Manage breeding schedules and track animal reproduction cycles", "breeding.schedule.action": "Add Breeding Schedule", "breeding.pregnancy": "Pregnancy Tracking", "breeding.predictions": "Birth Predictions", "breeding.upcoming_breedings": "Upcoming Breedings", "breeding.expected_births": "Expected Births", "breeding.status_scheduled": "Scheduled", "breeding.status_in_progress": "In Progress", "breeding.status_completed": "Completed", "breeding.status_failed": "Failed", "breeding.method_natural": "Natural", "breeding.method_ai": "AI", "breeding.method_embryo": "Embryo Transfer", "breeding.dam": "Dam", "breeding.sire": "<PERSON>e", "breeding.breeding_date": "Breeding Date", "breeding.expected_due_date": "Expected Due Date", "breeding.method": "Method", "breeding.status": "Status", "breeding.notes": "Notes", "health.dashboard": "Health Dashboard", "health.dashboard.title": "Health Management", "health.dashboard.subtitle": "Monitor and manage animal health records, treatments, vaccinations, and medical history", "health.dashboard.action": "Add Health Record", "health.manage": "Manage health records and treatments", "health.add": "Add Health Record", "health.health_statistics": "Health Statistics", "health.health_analytics": "Health Analytics", "health.health_trends": "Health Trends Over Time", "health.total_records": "Total Records", "health.health_records": "health records", "health.active_treatments": "Active Treatments", "health.vaccinations_due": "Vaccinations Due", "health.health_alerts": "Health Alerts", "health.vaccinations": "Vaccination Schedule", "health.diseases": "Disease Tracking", "health.treatments": "Treatment Records", "health.appointments": "Vet Appointments", "health.completed": "Completed", "health.scheduled": "Scheduled", "health.cancelled": "Cancelled", "health.records_by_type": "Records by Type", "health.upcoming_health_events": "Upcoming Health Events", "health.records": "Health Records", "health.records.subtitle": "Comprehensive health history and medical records for your livestock", "health.records.action": "Add Record", "health.record_summary": "Record Summary", "health.recent_health_timeline": "Recent Health Timeline", "health.abnormal_test_results": "Abnormal Test Results", "health.tab_all_records": "All Records", "health.tab_examinations": "Examinations", "health.tab_treatments": "Treatments", "health.tab_vaccinations": "Vaccinations", "health.tab_surgeries": "Surgeries", "health.tab_tests": "Tests", "health.animal": "Animal", "health.date": "Date", "health.record_type": "Record Type", "health.veterinarian": "Veterinarian", "health.diagnosis_treatment": "Diagnosis/Treatment", "health.follow_up": "Follow-up", "health.edit_health_record": "Edit Health Record", "health.add_new_health_record": "Add New Health Record", "feeding.dashboard": "Feeding Dashboard", "feeding.dashboard.title": "Feed Management Dashboard", "feeding.dashboard.subtitle": "Monitor feed inventory, track consumption, and manage feeding plans efficiently", "feeding.dashboard.action": "Add Feeding Record", "feeding.manage": "Manage feeding schedules and nutrition", "feeding.add": "Add Feeding Record", "feeding.feed_statistics": "Feed Statistics", "feeding.feed_used_today": "Feed Used Today", "feeding.inventory_value": "Inventory Value", "feeding.low_stock_items": "Low Stock Items", "feeding.active_feeding_plans": "Active Feeding Plans", "feeding.inventory": "Inventory", "feeding.plans": "Feeding Plans", "feeding.records": "Feeding Records", "feeding.suppliers": "Suppliers", "financial.dashboard": "Financial Dashboard", "financial.dashboard.title": "Financial Dashboard", "financial.dashboard.subtitle": "Monitor financial performance, revenue, expenses, and profitability metrics", "financial.dashboard.action": "Add Transaction", "financial.manage": "Manage financial records and transactions", "financial.add": "Add Financial Record", "financial.financial_management": "Financial Management", "financial.add_transaction": "Add Transaction", "financial.generate_reports": "Generate Reports", "financial.total_revenue": "Total Revenue", "financial.total_expenses": "Total Expenses", "financial.net_profit": "Net Profit", "financial.cash_on_hand": "Cash on Hand", "financial.expenses_by_category": "Expenses by Category", "financial.expenses_by_category_desc": "Distribution of expenses by category", "financial.revenue_by_source": "Revenue by Source", "financial.revenue_by_source_desc": "Distribution of revenue by source", "financial.monthly_financial_performance": "Monthly Financial Performance", "financial.monthly_financial_performance_desc": "Track revenue, expenses, and profit over time", "financial.transactions": "Transactions", "financial.recent_transactions": "Recent Transactions", "financial.recent_transactions_desc": "View and manage your latest financial transactions", "financial.roi": "ROI Calculator", "financial.forecast": "Forecasting", "financial.financial_forecast": "Financial Forecast", "financial.financial_forecast_desc": "Projected revenue and expenses for the next 6 months", "financial.invoices": "Invoicing", "financial.budget_vs_actual": "Budget vs. Actual", "financial.budget_vs_actual_desc": "Compare budgeted amounts with actual spending", "financial.financial_health": "Financial Health", "financial.financial_health_desc": "Key financial ratios and indicators", "financial.view_budget_details": "View Budget Details", "financial.view_detailed_forecast": "View Detailed Forecast", "financial.view_financial_health": "View Financial Health", "financial.view_all_transactions": "View All Transactions", "financial.loading": "Loading financial data...", "reports.dashboard": "Reports Dashboard", "reports.page.title": "Reports", "reports.page.subtitle": "Generate and view reports", "reports.manage": "Generate and view reports", "reports.create": "Create Report", "reports.analysis_reports": "Analysis Reports", "reports.performance_reports": "Performance Reports", "reports.health_reports": "Health Reports", "reports.market_reports": "Market Reports", "reports.financial_reports": "Financial Reports", "reports.custom_reports": "Custom Reports", "reports.reports": "Reports", "reports.analysis_description": "Comprehensive analysis of your livestock data with key insights and trends.", "reports.performance_description": "Track and measure the performance metrics of your livestock over time.", "reports.health_description": "Monitor health statistics, vaccination records, and medical treatments.", "reports.market_description": "Analyze market trends, pricing data, and sales opportunities.", "reports.financial_description": "Review financial statements, revenue streams, and expense breakdowns.", "reports.custom_description": "Create customized reports tailored to your specific needs.", "reports.reports_description": "Generate and download reports from your livestock management system.", "reports.generate_report": "Generate Report", "reports.saved_reports": "Saved Reports", "reports.scheduled_reports": "Scheduled Reports", "reports.saved_reports_description": "View and download your previously generated reports.", "reports.scheduled_reports_description": "Set up automatic report generation on a schedule.", "reports.saved_reports_empty": "Your saved reports will appear here after you generate them.", "reports.scheduled_reports_info": "You can schedule reports to be generated automatically on a daily, weekly, or monthly basis.", "reports.schedule_new_report": "Schedule New Report", "reports.financial": "Financial Reports", "reports.performance": "Performance Reports", "reports.health": "Health Reports", "reports.market": "Market Analysis", "reports.analysis": "Predictive Analysis", "reports.strategy": "Business Strategy", "reports.view": "View Reports", "settings.dashboard": "Settings Dashboard", "settings.title": "Settings", "settings.subtitle": "Configure your application settings and preferences", "settings.page.title": "Settings", "settings.page.subtitle": "Configure your application settings and preferences", "settings.manage": "Manage system settings and preferences", "settings.profile": "Profile Settings", "settings.account": "Account <PERSON><PERSON>", "settings.system": "System Settings", "settings.users": "Users", "settings.roles": "Role Management", "settings.permissions": "Permission Management", "settings.language": "Language", "settings.theme": "Theme", "settings.notifications": "Notifications", "settings.backup": "Backup & Restore", "settings.import": "Import Data", "settings.export": "Export Data", "settings.security": "Security", "settings.security_settings": "Security Settings", "settings.security_future_update": "Security settings will be implemented in a future update.", "settings.notification_settings": "Notification Settings", "settings.notification_future_update": "Notification settings will be implemented in a future update.", "settings.language_settings": "Language Settings", "settings.language_future_update": "Language settings will be implemented in a future update.", "commercial.manage": "Market and trade operations", "commercial.marketplace": "Marketplace", "commercial.orders": "Orders", "commercial.suppliers": "Suppliers", "commercial.pricing": "Price Tracking", "commercial.auctions": "Auctions", "compliance.manage": "Regulatory compliance", "compliance.certifications": "Certifications", "compliance.inspections": "Inspections", "compliance.documents": "Documentation", "resources.manage": "Guides and materials", "resources.guidelines": "Guidelines", "resources.training": "Training", "resources.support": "Support", "resources.government": "Government Resources", "resources.documentation": "Documentation", "resources.downloads": "Downloads", "resources.title": "Useful Resources for Farmers", "resources.landbank": "Land Bank", "resources.landbank_desc": "Financial services for the agricultural sector", "resources.arc": "Agricultural Research Council", "resources.arc_desc": "Research and development in agriculture", "resources.deeds": "Deeds Office", "resources.deeds_desc": "Property registration and information", "resources.namc": "National Agricultural Marketing Council", "resources.namc_desc": "Market access and development", "resources.ppecb": "PPECB", "resources.ppecb_desc": "Export certification and quality assurance", "settings.database": "Database Settings", "chart.no_data": "No data available", "chart.change_type": "Change chart type", "chart.type.bar": "Bar Chart", "chart.type.line": "Line Chart", "chart.type.area": "Area Chart", "chart.type.pie": "Pie Chart", "chart.type.radar": "Radar Chart", "chart.type.scatter": "Scatter Plot", "chart.time_range.day": "Today", "chart.time_range.week": "This Week", "chart.time_range.month": "This Month", "chart.time_range.quarter": "This Quarter", "chart.time_range.year": "This Year", "chart.time_range.all": "All Time", "table.no_data": "No data available", "table.selected": "selected", "table.actions": "Actions", "table.columns": "Columns", "table.rows_per_page": "Rows per page", "table.of": "of", "common.more": "More", "common.clear_all": "Clear All", "common.filters": "Filters", "dashboard.welcome.title": "Welcome to MayCaiphus Livestock", "dashboard.welcome.subtitle": "Your comprehensive livestock management solution. Monitor your animals, track health records, manage breeding, and more.", "dashboard.welcome.add_animal": "Add New Animal", "dashboard.welcome.view_reports": "View Reports", "dashboard.overview.title": "Overview", "dashboard.overview.total_animals": "Total Animals", "dashboard.overview.pending_tasks": "Pending Tasks", "dashboard.overview.health_alerts": "Health Alerts", "dashboard.overview.recent_births": "Recent Births", "dashboard.modules.title": "<PERSON><PERSON><PERSON>", "dashboard.activity.title": "Recent Activity", "dashboard.animals.title": "Animal Management", "dashboard.animals.description": "Manage your livestock inventory", "dashboard.animals.stats.total": "Total Animals", "dashboard.animals.stats.active": "Active", "dashboard.animals.submodules.profiles": "Animal Profiles", "dashboard.animals.submodules.health": "Health Records", "dashboard.animals.submodules.growth": "Growth Tracking", "dashboard.status.needs_attention": "Needs attention", "dashboard.status.updated_today": "Updated today", "dashboard.status.low_stock": "Low stock", "dashboard.breeding.title": "Breeding Management", "dashboard.breeding.description": "Track breeding cycles and pregnancies", "dashboard.breeding.stats.pregnant": "Pregnant Animals", "dashboard.breeding.stats.births": "Recent Births", "dashboard.breeding.submodules.records": "Breeding Records", "dashboard.breeding.submodules.heat": "Heat Calendar", "dashboard.breeding.submodules.pregnancy": "Pregnancy Tracking", "dashboard.health.title": "Health Management", "dashboard.health.description": "Monitor animal health and treatments", "dashboard.health.stats.alerts": "Health Alerts", "dashboard.health.stats.vaccinations": "Vaccinations Due", "dashboard.health.submodules.records": "Health Records", "dashboard.health.submodules.vaccinations": "Vaccination Schedule", "dashboard.health.submodules.treatments": "Treatment Plans", "dashboard.feeding.title": "Feed Management", "dashboard.feeding.description": "Manage feed inventory and schedules", "dashboard.feeding.stats.schedules": "Active Schedules", "dashboard.feeding.stats.low_stock": "Low Stock Items", "dashboard.feeding.submodules.schedules": "Feeding Schedules", "dashboard.feeding.submodules.inventory": "Feed Inventory", "dashboard.feeding.submodules.nutrition": "Nutrition Analysis", "dashboard.commercial.title": "Commercial", "dashboard.commercial.description": "Manage market operations and sales", "dashboard.commercial.stats.orders": "Active Orders", "dashboard.commercial.stats.revenue": "Revenue", "dashboard.commercial.submodules.orders": "Orders", "dashboard.commercial.submodules.marketplace": "Marketplace", "dashboard.commercial.submodules.suppliers": "Suppliers", "dashboard.financial.title": "Financial Management", "dashboard.financial.description": "Track expenses and revenue", "dashboard.financial.stats.revenue": "Monthly Revenue", "dashboard.financial.stats.expenses": "Monthly Expenses", "dashboard.financial.submodules.overview": "Financial Overview", "dashboard.financial.submodules.transactions": "Transactions", "dashboard.financial.submodules.reports": "Reports", "dashboard.reports.title": "Reports & Analytics", "dashboard.reports.description": "Generate insights and reports", "dashboard.reports.stats.saved": "Saved Reports", "dashboard.reports.stats.metrics": "Custom Metrics", "dashboard.business_analysis_description": "Comprehensive analysis of your livestock business performance", "dashboard.strategy": "Business Strategy", "dashboard.predictions": "Business Predictions", "dashboard.reports.submodules.analysis": "Analysis", "dashboard.reports.submodules.performance": "Performance Metrics", "dashboard.reports.submodules.financial": "Financial Reports", "dashboard.reports.submodules.health": "Health Statistics", "dashboard.reports.submodules.market": "Market Analysis", "dashboard.reports.submodules.custom": "Custom Reports", "dashboard.business_analysis": "Business Analysis Dashboard", "dashboard.loading_analysis": "Loading business analysis data...", "dashboard.refresh": "Refresh Data", "dashboard.download": "Download Report", "dashboard.print": "Print Report", "dashboard.overview": "Overview", "dashboard.financial": "Financial", "dashboard.animals": "Animals", "dashboard.operations": "Operations", "dashboard.compliance.title": "Compliance", "dashboard.compliance.description": "Manage regulatory compliance", "dashboard.compliance.stats.documents": "Documents", "dashboard.compliance.stats.certifications": "Certifications", "dashboard.compliance.submodules.certifications": "Certifications", "dashboard.compliance.submodules.inspections": "Inspections", "dashboard.compliance.submodules.documentation": "Documentation", "dashboard.settings.title": "Settings", "dashboard.settings.description": "Configure system settings", "dashboard.settings.stats.users": "User Accounts", "dashboard.settings.stats.version": "System Version", "dashboard.settings.submodules.users": "User Management", "dashboard.settings.submodules.theme": "Theme Settings", "dashboard.settings.submodules.database": "Database Settings", "dashboard.settings.submodules.backup": "Backup & Restore"}
const { MongoClient } = require('mongodb');

const uri = "mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const dbName = "ampd_livestock";

async function testConnection() {
  try {
    console.log("Testing MongoDB connection...");

    const client = new MongoClient(uri, {
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      retryWrites: true,
      w: 'majority',
    });

    await client.connect();
    console.log("Successfully connected to MongoDB!");

    const db = client.db(dbName);
    const stats = await db.command({ dbStats: 1 });
    console.log("Database stats:", stats);

    await client.close();
    console.log("Disconnected from MongoDB.");
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
  }
}

testConnection();
